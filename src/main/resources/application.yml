server:
  tomcat:
    uri-encoding: UTF-8
    threads:
      max: 1000
      min-spare: 30
  port: 38383
#  servlet:
#    context-path: /
#    session:
#      cookie:
#        http-only: true

spring:
  # 环境 dev|test|prod
  profiles:
#    active: dev
    active: prod
  application:
    name: bto-task
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true
  mvc:
    throw-exception-if-no-handler-found: true
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER

bto:
  xss:
    enabled: true

#文档
knife4j:
  enable: true
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
    show-extensions: true
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: 'default'
      paths-to-match: '/**'
      packages-to-scan: com.botong

mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  # 实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.botong.*.entity
  global-config:
    # 数据库相关配置
    db-config:
      # ID自增
      id-type: AUTO
      # 逻辑已删除值
      logic-delete-value: 1
      # 逻辑未删除值
      logic-not-delete-value: 0
    banner: false
  # 原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  configuration-properties:
    prefix:
    blobType: BLOB
    boolValue: TRUE