package com.botong.monitor.controller;

import com.botong.common.Result;
import com.botong.monitor.service.MonitorService;
import com.botong.monitor.vo.MonitorServerVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> by zhb on 2023/12/6.
 */

@RestController
@RequestMapping("monitor")
@Tag(name = "监控管理")
@AllArgsConstructor
public class MonitorController {

    private final MonitorService devMonitorService;

    /**
     * 获取服务器监控信息
     */
    @Operation(summary = "获取服务器监控信息")
    @GetMapping("serverInfo")
    public Result<MonitorServerVO> serverInfo() {
        return Result.ok(devMonitorService.serverInfo());
    }

    /**
     * 获取服务器网络情况
     */
    @Operation(summary = "获取服务器网络情况")
    @GetMapping("networkInfo")
    public Result<MonitorServerVO> networkInfo() {
        return Result.ok(devMonitorService.networkInfo());
    }
}
