package com.botong.task.service.impl;

import com.botong.enums.SuperAdminEnum;
import com.botong.enums.UserStatusEnum;
import com.botong.security.user.UserDetail;
import com.botong.task.convert.UserConvert;
import com.botong.task.entity.UserEntity;
import com.botong.task.service.AuthUserDetailsService;
import lombok.AllArgsConstructor;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Set;

/**
 * 用户 UserDetails 信息
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class AuthUserDetailsServiceImpl implements AuthUserDetailsService {

    @Override
    public UserDetails getUserDetails(UserEntity userEntity) {
        // 转换成UserDetail对象
        UserDetail userDetail = UserConvert.INSTANCE.convertDetail(userEntity);

        // 账号不可用
        if (userEntity.getStatus() == UserStatusEnum.DISABLE.getValue()) {
            userDetail.setEnabled(false);
        }

        Set<String> permsSet = new HashSet<>();
        // 时间紧、任务重，无菜单权限配置，目前通过硬编码方式添加权限，后续完善
        if (userDetail.getSuperAdmin().equals(SuperAdminEnum.YES.getValue())) {
            permsSet.add("task:quest:updateTaskStatus");
        }
        userDetail.setAuthoritySet(permsSet);

        return userDetail;
    }

}