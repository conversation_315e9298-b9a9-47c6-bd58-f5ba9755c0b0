package com.botong.task.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.botong.task.dao.TaskUserDao;
import com.botong.task.entity.TaskUserEntity;
import com.botong.task.service.TaskUserService;
import lombok.AllArgsConstructor;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 研发用户任务关系
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-20
 */
@Service
@AllArgsConstructor
public class TaskUserServiceImpl extends BaseServiceImpl<TaskUserDao, TaskUserEntity> implements TaskUserService {

    private final ApplicationContext applicationContext;

    @Override
    public void insert(List<String> userIds, String taskId) {
        userIds.forEach(userId -> {
            TaskUserEntity taskUserEntity = new TaskUserEntity();
            taskUserEntity.setTaskId(taskId);
            taskUserEntity.setUserId(userId);
            baseMapper.insert(taskUserEntity);
        });
    }

    @Override

    public List<String> getUserIdsByTaskId(String taskId) {
        LambdaQueryWrapper<TaskUserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskUserEntity::getTaskId, taskId);
        return baseMapper.selectList(wrapper).stream().map(TaskUserEntity::getUserId).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(List<String> userIds, String taskId) {
        LambdaQueryWrapper<TaskUserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskUserEntity::getTaskId, taskId);
        baseMapper.delete(wrapper);
        TaskUserServiceImpl taskService = applicationContext.getBean(TaskUserServiceImpl.class);
        taskService.insert(userIds, taskId);
    }

    @Override
    public List<String> getTaskIdsByUserIds(List<String> userIds) {
        LambdaQueryWrapper<TaskUserEntity> wrapper = new LambdaQueryWrapper<>();
        if (!userIds.isEmpty()) {
            wrapper.in(TaskUserEntity::getUserId, userIds);
            return baseMapper.selectList(wrapper).stream().map(TaskUserEntity::getTaskId).collect(Collectors.toList());
        }
        return null;
    }
}