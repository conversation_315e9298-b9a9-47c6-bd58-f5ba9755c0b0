package com.botong.task.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.common.PageResult;
import com.botong.enums.TaskGradeEnum;
import com.botong.enums.TaskStatusEnum;
import com.botong.excel.ExcelFinishCallBack;
import com.botong.exception.ServerException;
import com.botong.server.vo.TaskExcelVO;
import com.botong.task.convert.TaskConvert;
import com.botong.task.convert.TaskExcelConvert;
import com.botong.task.dao.TaskDao;
import com.botong.task.dto.TaskStatusUpdateDTO;
import com.botong.task.entity.TaskEntity;
import com.botong.task.entity.UserEntity;
import com.botong.task.query.TaskQuery;
import com.botong.task.service.TaskService;
import com.botong.task.service.TaskUserService;
import com.botong.task.service.UserService;
import com.botong.task.vo.TaskVO;
import com.botong.utils.AssertUtils;
import com.botong.utils.DateUtils;
import com.botong.utils.ExcelUtils;
import lombok.AllArgsConstructor;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 研发任务
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-18
 */
@Service
@AllArgsConstructor
public class TaskServiceImpl extends BaseServiceImpl<TaskDao, TaskEntity> implements TaskService {

    private final TaskUserService taskUserService;
    private final UserService userService;

    @Override
    public PageResult<TaskVO> page(TaskQuery query) {
        LambdaQueryWrapper<TaskEntity> wrapper = getWrapper(query);
        String projectId = query.getProjectId();
        boolean projectIdNotBlank = StrUtil.isNotEmpty(projectId);
        if (projectIdNotBlank) {
            List<String> userIds = userService.getUserIdsByProjectId(projectId);
            List<String> taskIds = taskUserService.getTaskIdsByUserIds(userIds);
            if (taskIds.isEmpty()) {
                return new PageResult<>(null, 0);
            }
            wrapper.in(TaskEntity::getTaskId, taskIds);
        }
        IPage<TaskEntity> page = baseMapper.selectPage(getPage(query), wrapper);

        List<TaskVO> taskVos = TaskConvert.INSTANCE.convertList(page.getRecords());
        taskVos.forEach(taskVo -> {
            updateStatusAndProgress(taskVo);
            taskVo.setTaskGradeLabel(TaskGradeEnum.getNameByValue(taskVo.getTaskGrade()));

            List<String> userIds = taskUserService.getUserIdsByTaskId(taskVo.getTaskId());
            if (!userIds.isEmpty()) {
                List<String> userNames = userService.listByIds(userIds).stream().map(UserEntity::getUsername).collect(Collectors.toList());
                taskVo.setUserList(userNames);
            }
        });

        return new PageResult<>(taskVos, page.getTotal());
    }

    private LambdaQueryWrapper<TaskEntity> getWrapper(TaskQuery query) {
        LambdaQueryWrapper<TaskEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StrUtil.isNotEmpty(query.getTaskId()), TaskEntity::getTaskId, query.getTaskId());
        wrapper.like(StrUtil.isNotEmpty(query.getTaskName()), TaskEntity::getTaskName, query.getTaskName());
        wrapper.eq(Objects.nonNull(query.getTaskGrade()), TaskEntity::getTaskGrade, query.getTaskGrade());
        wrapper.orderByDesc(TaskEntity::getStartDate);
        return wrapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(TaskVO vo) {
        TaskEntity entity = TaskConvert.INSTANCE.convert(vo);
        entity.setStartDate(DateUtil.beginOfDay(entity.getStartDate()));
        entity.setEndDate(DateUtil.endOfDay(entity.getEndDate()));
        List<String> userIds = userService.getUserIdsByUsernames(vo.getUserList());
        if (userIds.isEmpty()) {
            throw new ServerException("研发人员填写不正确或不存在");
        }
        baseMapper.insert(entity);
        taskUserService.insert(userIds, entity.getTaskId());

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TaskVO vo) {
        AssertUtils.isNull(vo.getTaskId(), "taskId");
        TaskEntity entity = TaskConvert.INSTANCE.convert(vo);

        List<String> userIds = userService.getUserIdsByUsernames(vo.getUserList());
        if (userIds.isEmpty()) {
            throw new ServerException("研发人员填写不正确或不存在");
        }

        taskUserService.update(userIds, entity.getTaskId());

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<String> idList) {
        removeByIds(idList);
    }


    @Override
    public void updateTaskStatus(TaskStatusUpdateDTO dto) {
        String taskId = dto.getTaskId();
        TaskEntity taskEntity = baseMapper.selectById(taskId);
        AssertUtils.isNull(taskEntity, "task");

        Date startDate = taskEntity.getStartDate();
        Date endDate = taskEntity.getEndDate();
        Date finishDate = new Date();

        if (finishDate.getTime() < startDate.getTime()) {
            throw new ServerException("任务还没开始就想完成了？");
        }

        taskEntity.setFinishDate(finishDate);
        taskEntity.setRemark(dto.getRemark());
        baseMapper.updateById(taskEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importByExcel(MultipartFile file) {
        ExcelUtils.readAnalysis(file, TaskExcelVO.class, new ExcelFinishCallBack<TaskExcelVO>() {
            @Override
            public void doAfterAllAnalysed(List<TaskExcelVO> result) {
                saveWorkOrderApplication(result);
            }

            @Override
            public void doSaveBatch(List<TaskExcelVO> result) {
                saveWorkOrderApplication(result);
            }

            private void saveWorkOrderApplication(List<TaskExcelVO> result) {
                excelDataConversion(result, ExcelUtils.IMPORT);
                List<TaskVO> list = TaskConvert.INSTANCE.convertTaskVoList(result);
                list.forEach(taskVO -> {
                    // Excel导入默认值
                    // 防止事务失效
                    try {
                        TaskService taskService = (TaskService) AopContext.currentProxy();
                        taskService.save(taskVO);
                    } catch (Exception e) {
                        throw new ServerException(e.getMessage());
                    }
                });
            }
        });
    }

    @Override
    public void export(String projectId) {
        LambdaQueryWrapper<TaskEntity> wrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isNotBlank(projectId)) {
            List<String> userIds = userService.getUserIdsByProjectId(projectId);
            List<String> taskIds = taskUserService.getTaskIdsByUserIds(userIds);
            if (taskIds.isEmpty()) {
                throw new ServerException("暂无数据");
            }
            wrapper.in(TaskEntity::getTaskId, taskIds);
        }
        List<TaskEntity> taskEntities = baseMapper.selectList(wrapper);
        List<TaskExcelVO> taskExcelVos = TaskExcelConvert.INSTANCE.convertList(taskEntities);
        taskExcelVos.forEach(taskExcelVO -> {
            List<String> userIds = taskUserService.getUserIdsByTaskId(taskExcelVO.getTaskId());
            if (!userIds.isEmpty()) {
                List<String> userNames = userService.listByIds(userIds).stream().map(UserEntity::getUsername).collect(Collectors.toList());
                taskExcelVO.setUserList(userNames);
            }
        });

        excelDataConversion(taskExcelVos, ExcelUtils.EXPORT);
        // 写到浏览器打开
        ExcelUtils.excelExport(TaskExcelVO.class, "task_excel_excel" + DateUtils.format(new Date()), null, taskExcelVos);
    }

    private void excelDataConversion(List<TaskExcelVO> taskExcelVos, String type) {
        if (ExcelUtils.IMPORT.equals(type)) {
            taskExcelVos.forEach(taskExcel -> {
                taskExcel.setTaskGrade(TaskGradeEnum.getValueByName(taskExcel.getTaskGradeLabel()));
                taskExcel.setUserList(Arrays.asList(taskExcel.getUsers().split("、")));
            });
        }
        if (ExcelUtils.EXPORT.equals(type)) {
            taskExcelVos.forEach(taskExcel -> {
                taskExcel.setTaskGradeLabel(TaskGradeEnum.getNameByValue(taskExcel.getTaskGrade()));
                List<String> userList = taskExcel.getUserList();
                if (Objects.nonNull(userList)) {
                    taskExcel.setUsers(userList.toString());
                }
            });
        }
    }

    public Double calculateProgress(Date startTime, Date endTime, Date nowDate) {
        Double progress = null;
        if (nowDate.getTime() <= startTime.getTime()) {
            progress = 0.0;
        } else {
            // 结束时间和开始时间中间的小时数
            long totalDuration = DateUtil.between(startTime, endTime, DateUnit.HOUR);
            // 当前时间和开始时间中间的小时数
            long elapsedDuration = DateUtil.between(startTime, nowDate, DateUnit.HOUR);
            progress = NumberUtil.div(elapsedDuration, totalDuration);
        }
        return progress;
    }

    public void updateStatusAndProgress(TaskVO taskVO) {
        Date startDate = taskVO.getStartDate();
        Date endDate = taskVO.getEndDate();
        Date nowDate = new Date();
        if (Objects.nonNull(startDate) && Objects.nonNull(endDate)) {
            if (nowDate.getTime() <= startDate.getTime()) {
                taskVO.setProgress(0.0);
                taskVO.setTaskStatusLabel(TaskStatusEnum.NOT_STARTED.getName());
            } else if (nowDate.getTime() <= endDate.getTime() && Objects.isNull(taskVO.getFinishDate())) {
                taskVO.setTaskStatusLabel(TaskStatusEnum.UNDERWAY.getName());
                taskVO.setProgress(calculateProgress(startDate, endDate, nowDate));
            } else if (nowDate.getTime() > endDate.getTime() && Objects.isNull(taskVO.getFinishDate())) {
                taskVO.setTaskStatusLabel(TaskStatusEnum.TIMEOUT.getName());
                taskVO.setProgress(calculateProgress(startDate, endDate, nowDate));
            } else {
                taskVO.setTaskStatusLabel(TaskStatusEnum.COMPLETED.getName());
                taskVO.setProgress(calculateProgress(startDate, endDate, taskVO.getFinishDate()));
            }
        }
    }

}