package com.botong.task.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.common.PageResult;
import com.botong.task.convert.UserConvert;
import com.botong.task.dao.UserDao;
import com.botong.task.entity.UserEntity;
import com.botong.task.query.UserQuery;
import com.botong.task.service.UserService;
import com.botong.task.vo.UserVO;
import com.botong.utils.AssertUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 研发人员
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-17
 */
@Service
@AllArgsConstructor
public class UserServiceImpl extends BaseServiceImpl<UserDao, UserEntity> implements UserService {

    @Override
    public PageResult<UserVO> page(UserQuery query) {
        IPage<UserEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

        return new PageResult<>(UserConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
    }

    private LambdaQueryWrapper<UserEntity> getWrapper(UserQuery query) {
        LambdaQueryWrapper<UserEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StrUtil.isNotEmpty(query.getProjectId()), UserEntity::getProjectId, query.getProjectId());
        wrapper.like(StrUtil.isNotEmpty(query.getUsername()), UserEntity::getUsername, query.getUsername());
        return wrapper;
    }

    @Override
    public void save(UserVO vo) {
        UserEntity entity = UserConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    @Override
    public void update(UserVO vo) {
        AssertUtils.isNull(vo.getUserId(), "userId");
        UserEntity entity = UserConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<String> idList) {
        removeByIds(idList);
    }

    @Override
    public List<String> getUserIdsByUsernames(List<String> userList) {
        LambdaQueryWrapper<UserEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.in(UserEntity::getUsername, userList);
        return baseMapper.selectList(wrapper).stream().map(UserEntity::getUserId).collect(Collectors.toList());

    }

    @Override
    public List<String> getUserIdsByProjectId(String projectId) {
        LambdaQueryWrapper<UserEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(UserEntity::getProjectId, projectId);
        return baseMapper.selectList(wrapper).stream().map(UserEntity::getUserId).collect(Collectors.toList());
    }

    @Override
    public UserVO getUserByUsername(String username) {
        LambdaQueryWrapper<UserEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserEntity::getUsername, username);
        UserEntity userEntity = baseMapper.selectOne(queryWrapper);
        return UserConvert.INSTANCE.convert(userEntity);
    }

}