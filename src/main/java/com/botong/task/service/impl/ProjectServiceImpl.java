package com.botong.task.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.botong.common.PageResult;
import lombok.AllArgsConstructor;
import com.botong.task.convert.ProjectConvert;
import com.botong.task.entity.ProjectEntity;
import com.botong.task.query.ProjectQuery;
import com.botong.task.vo.ProjectVO;
import com.botong.task.dao.ProjectDao;
import com.botong.task.service.ProjectService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 研发项目
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-18
 */
@Service
@AllArgsConstructor
public class ProjectServiceImpl extends BaseServiceImpl<ProjectDao, ProjectEntity> implements ProjectService {

    @Override
    public PageResult<ProjectVO> page(ProjectQuery query) {
        IPage<ProjectEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));
        return new PageResult<>(ProjectConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
    }

    private LambdaQueryWrapper<ProjectEntity> getWrapper(ProjectQuery query) {
        LambdaQueryWrapper<ProjectEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StrUtil.isNotEmpty(query.getProjectName()), ProjectEntity::getProjectName, query.getProjectName());
        return wrapper;
    }

    @Override
    public void save(ProjectVO vo) {
        ProjectEntity entity = ProjectConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    @Override
    public void update(ProjectVO vo) {
        ProjectEntity entity = ProjectConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<String> idList) {
        removeByIds(idList);
    }

}