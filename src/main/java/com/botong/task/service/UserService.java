package com.botong.task.service;


import com.botong.common.PageResult;
import com.botong.task.entity.UserEntity;
import com.botong.task.query.UserQuery;
import com.botong.task.vo.UserVO;

import java.util.List;

/**
 * 研发人员
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-17
 */
public interface UserService extends BaseService<UserEntity> {

    PageResult<UserVO> page(UserQuery query);

    void save(UserVO vo);

    void update(UserVO vo);

    void delete(List<String> idList);

    List<String> getUserIdsByUsernames(List<String> userList);

    List<String> getUserIdsByProjectId(String projectId);

    UserVO getUserByUsername(String username);
}