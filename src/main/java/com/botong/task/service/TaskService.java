package com.botong.task.service;

import com.botong.common.PageResult;
import com.botong.task.dto.TaskStatusUpdateDTO;
import com.botong.task.entity.TaskEntity;
import com.botong.task.query.TaskQuery;
import com.botong.task.vo.TaskVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 研发任务
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-18
 */
public interface TaskService extends BaseService<TaskEntity> {

    PageResult<TaskVO> page(TaskQuery query);

    void save(TaskVO vo);

    void update(TaskVO vo);

    void delete(List<String> idList);

    void updateTaskStatus(TaskStatusUpdateDTO dto);

    void importByExcel(MultipartFile file);

    void export(String projectId);

}