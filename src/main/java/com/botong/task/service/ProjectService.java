package com.botong.task.service;

import com.botong.common.PageResult;
import com.botong.task.vo.ProjectVO;
import com.botong.task.query.ProjectQuery;
import com.botong.task.entity.ProjectEntity;

import java.util.List;

/**
 * 研发项目
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-18
 */
public interface ProjectService extends BaseService<ProjectEntity> {

    PageResult<ProjectVO> page(ProjectQuery query);

    void save(ProjectVO vo);

    void update(ProjectVO vo);

    void delete(List<String> idList);
}