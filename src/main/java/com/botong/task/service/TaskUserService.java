package com.botong.task.service;


import com.botong.task.entity.TaskUserEntity;

import java.util.List;

/**
 * 研发用户任务关系
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-20
 */
public interface TaskUserService extends BaseService<TaskUserEntity> {

    void insert(List<String> userIds, String taskId);

    List<String> getUserIdsByTaskId(String taskId);

    void update(List<String> userIds, String taskId);

    List<String> getTaskIdsByUserIds(List<String> userIds);

}