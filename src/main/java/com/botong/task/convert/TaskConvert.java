package com.botong.task.convert;

import com.botong.server.vo.TaskExcelVO;
import com.botong.task.entity.TaskEntity;
import com.botong.task.vo.TaskVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 研发任务
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-18
 */
@Mapper
public interface TaskConvert {
    TaskConvert INSTANCE = Mappers.getMapper(TaskConvert.class);

    TaskEntity convert(TaskVO vo);

    TaskVO convert(TaskEntity entity);

    TaskVO convert(TaskExcelVO taskExcelVO);

    List<TaskVO> convertList(List<TaskEntity> list);

    List<TaskVO> convertTaskVoList(List<TaskExcelVO> list);

}