package com.botong.task.convert;

import com.botong.security.user.UserDetail;
import com.botong.task.entity.UserEntity;
import com.botong.task.vo.UserVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 研发人员
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-17
 */
@Mapper
public interface UserConvert {
    UserConvert INSTANCE = Mappers.getMapper(UserConvert.class);

    UserEntity convert(UserVO vo);

    UserVO convert(UserEntity entity);

    List<UserVO> convertList(List<UserEntity> list);

    UserDetail convertDetail(UserEntity userEntity);
}