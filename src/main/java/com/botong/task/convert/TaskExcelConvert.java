package com.botong.task.convert;

import com.botong.server.vo.TaskExcelVO;
import com.botong.task.entity.TaskEntity;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR> by 2022 on 2023/12/1.
 */
@Mapper
public interface TaskExcelConvert {
    TaskExcelConvert INSTANCE = Mappers.getMapper(TaskExcelConvert.class);

    TaskExcelVO convert(TaskEntity taskEntity);

    List<TaskExcelVO> convertList(List<TaskEntity> list);
}