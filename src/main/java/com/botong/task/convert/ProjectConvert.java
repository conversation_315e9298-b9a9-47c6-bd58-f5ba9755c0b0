package com.botong.task.convert;

import com.botong.task.entity.ProjectEntity;
import com.botong.task.vo.ProjectVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 研发项目
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-18
 */
@Mapper
public interface ProjectConvert {
    ProjectConvert INSTANCE = Mappers.getMapper(ProjectConvert.class);

    ProjectEntity convert(ProjectVO vo);

    ProjectVO convert(ProjectEntity entity);

    List<ProjectVO> convertList(List<ProjectEntity> list);

}