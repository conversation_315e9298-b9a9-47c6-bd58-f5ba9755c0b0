package com.botong.task.controller;

import com.botong.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 权限测试
 * <AUTHOR>
 */
@RestController
@RequestMapping("test")
@Tag(name = "新模块测试")
@AllArgsConstructor
public class TestController {

    private final PasswordEncoder passwordEncoder;

    @GetMapping()
    @Operation(summary = "权限测试接口")
    public Result<String> test() {
        return Result.ok(passwordEncoder.encode("btoadmin"));
    }
}