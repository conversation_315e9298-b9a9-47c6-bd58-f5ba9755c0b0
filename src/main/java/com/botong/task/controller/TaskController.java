package com.botong.task.controller;

import com.botong.common.PageResult;
import com.botong.common.Result;
import com.botong.task.convert.TaskConvert;
import com.botong.task.dto.TaskStatusUpdateDTO;
import com.botong.task.entity.TaskEntity;
import com.botong.task.query.TaskQuery;
import com.botong.task.service.TaskService;
import com.botong.task.vo.TaskVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * 研发任务
 * <AUTHOR>
 * @since 1.0.0 2023-11-18
 */
@RestController
@RequestMapping("quest")
@Tag(name = "研发任务")
@AllArgsConstructor
@Validated
public class TaskController {
    private final TaskService taskService;

    @PostMapping("page")
    @Operation(summary = "分页")
    public Result<PageResult<TaskVO>> page(@Valid @RequestBody TaskQuery query) {
        PageResult<TaskVO> page = taskService.page(query);

        return Result.ok(page);
    }

    @PostMapping("import")
    @Operation(summary = "excel导入")
    public Result<String> importExcel(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return Result.error("请选择需要上传的文件");
        }
        taskService.importByExcel(file);
        return Result.ok();
    }

    @GetMapping("export")
    @Operation(summary = "excel导出")
    public void export(String projectId) {
        taskService.export(projectId);
    }

    @GetMapping("{id}")
    @Operation(summary = "信息")
    public Result<TaskVO> get(@PathVariable("id") String id) {
        TaskEntity entity = taskService.getById(id);

        return Result.ok(TaskConvert.INSTANCE.convert(entity));
    }

    @PostMapping
    @Operation(summary = "保存")
    public Result<String> save(@RequestBody @Valid TaskVO vo) {
        taskService.save(vo);

        return Result.ok();
    }

    @PutMapping
    @Operation(summary = "修改", description = "taskId不能为空")
    public Result<String> update(@RequestBody @Valid TaskVO vo) {
        taskService.update(vo);

        return Result.ok();
    }

    @DeleteMapping
    @Operation(summary = "删除")
    public Result<String> delete(@RequestBody List<String> idList) {
        taskService.delete(idList);

        return Result.ok();
    }

    @PutMapping("updateTaskStatus")
    @Operation(summary = "已完成更新")
    @PreAuthorize("hasAuthority('task:quest:updateTaskStatus')")
    public Result<String> updateTaskStatus(@RequestBody @Valid TaskStatusUpdateDTO dto) {
        taskService.updateTaskStatus(dto);

        return Result.ok();
    }
}