package com.botong.task.controller;

import com.botong.common.Result;
import com.botong.security.utils.TokenUtils;
import com.botong.task.service.AuthService;
import com.botong.task.vo.LoginVO;
import com.botong.task.vo.TokenVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 认证管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("auth")
@Tag(name = "认证管理")
@AllArgsConstructor
public class AuthController {

    private final AuthService authService;

    @PostMapping("login")
    @Operation(summary = "登录")
    public Result<TokenVO> login(@RequestBody LoginVO login) {
        TokenVO token = authService.loginByAccount(login);
        return Result.ok(token);
    }


    @PostMapping("logout")
    @Operation(summary = "退出")
    public Result<String> logout(HttpServletRequest request) {
        authService.logout(TokenUtils.getAccessToken(request));

        return Result.ok();
    }
}