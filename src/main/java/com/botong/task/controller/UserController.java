package com.botong.task.controller;

import cn.hutool.core.util.StrUtil;
import com.botong.common.PageResult;
import com.botong.common.Result;
import com.botong.task.convert.UserConvert;
import com.botong.task.entity.UserEntity;
import com.botong.task.query.UserQuery;
import com.botong.task.service.UserService;
import com.botong.task.vo.UserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 研发人员
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-17
 */
@RestController
@RequestMapping("user")
@Tag(name = "研发人员")
@AllArgsConstructor
@Validated
public class UserController {
    private final UserService userService;
    private final PasswordEncoder passwordEncoder;

    @PostMapping("page")
    @Operation(summary = "分页")
    public Result<PageResult<UserVO>> page(@Valid @RequestBody UserQuery query) {
        PageResult<UserVO> page = userService.page(query);

        return Result.ok(page);
    }

    @GetMapping("{id}")
    @Operation(summary = "信息")
    public Result<UserVO> get(@PathVariable("id") String id) {
        UserEntity entity = userService.getById(id);

        return Result.ok(UserConvert.INSTANCE.convert(entity));
    }

    @PostMapping
    @Operation(summary = "保存")
    public Result<String> save(@RequestBody @Valid UserVO vo) {

        // 新增密码不能为空
        if (StrUtil.isBlank(vo.getPassword())) {
            Result.error("密码不能为空");
        }

        // 密码加密
        vo.setPassword(passwordEncoder.encode(vo.getPassword()));

        userService.save(vo);

        return Result.ok();
    }

    @PutMapping
    @Operation(summary = "修改", description = "userId不能为空")
    public Result<String> update(@RequestBody @Valid UserVO vo) {
        userService.update(vo);

        return Result.ok();
    }

    @DeleteMapping
    @Operation(summary = "删除")
    public Result<String> delete(@RequestBody List<String> idList) {
        userService.delete(idList);

        return Result.ok();
    }
}