package com.botong.task.controller;

import com.botong.common.PageResult;
import com.botong.common.Result;
import com.botong.task.convert.ProjectConvert;
import com.botong.task.entity.ProjectEntity;
import com.botong.task.query.ProjectQuery;
import com.botong.task.service.ProjectService;
import com.botong.task.vo.ProjectVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 研发项目
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-18
 */
@RestController
@RequestMapping("project")
@Tag(name = "研发项目")
@AllArgsConstructor
@Validated
public class ProjectController {
    private final ProjectService projectService;

    @PostMapping("page")
    @Operation(summary = "分页")
    public Result<PageResult<ProjectVO>> page(@Valid @RequestBody ProjectQuery query) {
        PageResult<ProjectVO> page = projectService.page(query);

        return Result.ok(page);
    }

    @GetMapping("{id}")
    @Operation(summary = "信息")
    public Result<ProjectVO> get(@PathVariable("id") String id) {
        ProjectEntity entity = projectService.getById(id);

        return Result.ok(ProjectConvert.INSTANCE.convert(entity));
    }

    @PostMapping
    @Operation(summary = "保存")
    public Result<String> save(@RequestBody ProjectVO vo) {
        projectService.save(vo);

        return Result.ok();
    }

    @PutMapping
    @Operation(summary = "修改")
    public Result<String> update(@RequestBody @Valid ProjectVO vo) {
        projectService.update(vo);

        return Result.ok();
    }

    @DeleteMapping
    @Operation(summary = "删除")
    public Result<String> delete(@RequestBody List<String> idList) {
        projectService.delete(idList);

        return Result.ok();
    }
}