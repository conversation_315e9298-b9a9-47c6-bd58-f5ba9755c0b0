package com.botong.task.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 研发项目
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-18
 */

@Data
@TableName("dev_project")
public class ProjectEntity {

    /**
     * 项目ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

}