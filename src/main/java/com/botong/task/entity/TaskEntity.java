package com.botong.task.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 研发任务
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-18
 */

@Data
@TableName("dev_task")
public class TaskEntity {
    /**
     * 事项ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String taskId;

    /**
     * 事项名称
     */
    private String taskName;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 任务完成时间
     */
    private Date finishDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 任务级别：0->一级；1->二级；2->三级
     */
    private Integer taskGrade;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

}