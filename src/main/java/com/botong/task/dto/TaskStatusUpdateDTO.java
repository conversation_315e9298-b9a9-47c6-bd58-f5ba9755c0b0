package com.botong.task.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> by 2022 on 2023/11/20.
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "研发任务状态更新")
public class TaskStatusUpdateDTO {

    @Schema(description = "事项ID")
    @NotNull(message = "事项ID不能为空")
    private String taskId;

    @Schema(description = "备注")
    private String remark;

}