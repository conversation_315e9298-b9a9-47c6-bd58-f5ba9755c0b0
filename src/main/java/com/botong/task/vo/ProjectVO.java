package com.botong.task.vo;

import com.botong.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 研发项目
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-18
 */
@Data
@Schema(description = "研发项目")
public class ProjectVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "项目ID")
    private String projectId;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = DateUtils.DATE_PATTERN)
    private Date startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = DateUtils.DATE_PATTERN)
    private Date endTime;

}