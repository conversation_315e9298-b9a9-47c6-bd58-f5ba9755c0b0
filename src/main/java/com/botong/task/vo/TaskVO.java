package com.botong.task.vo;

import com.botong.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 研发任务
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-18
 */
@Data
@Schema(description = "研发任务")
public class TaskVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "事项ID")
    private String taskId;

    @Schema(description = "事项名称")
    @NotNull(message = "事项名称不能为空")
    private String taskName;

    @Schema(description = "开始时间")
    @NotNull(message = "开始时间不能为空")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date startDate;

    @Schema(description = "结束时间")
    @NotNull(message = "结束时间不能为空")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date endDate;

    @Schema(description = "任务完成时间")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date finishDate;

    @Schema(description = "任务状态")
    private String taskStatusLabel;

    @Schema(description = "任务级别：0->一级；1->二级；2->三级")
    @Range(min = 0, max = 2, message = "任务级别不正确")
    private Integer taskGrade;

    @Schema(description = "任务级别")
    private String taskGradeLabel;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "进度")
    private Double progress;

    @Schema(description = "研发人员")
    @NotNull(message = "研发人员不能为空")
    private List<String> userList;

}