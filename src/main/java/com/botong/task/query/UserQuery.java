package com.botong.task.query;

import com.botong.common.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 研发人员查询
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "研发人员查询")
public class UserQuery extends Query {

    @Schema(description = "项目ID")
    private String projectId;

    @Schema(description = "用户名称")
    private String username;

}