package com.botong.task.query;

import com.botong.common.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 研发项目查询
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "研发项目查询")
public class ProjectQuery extends Query {

    @Schema(description = "项目名称")
    private String projectName;

}