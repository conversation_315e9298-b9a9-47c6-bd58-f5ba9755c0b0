package com.botong.task.query;

import com.botong.common.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Range;

/**
 * 研发任务查询
 * <AUTHOR>
 * @since 1.0.0 2023-11-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "研发任务查询")
public class TaskQuery extends Query {

    @Schema(description = "事项ID")
    private String taskId;

    @Schema(description = "事项名称")
    private String taskName;

    @Schema(description = "项目ID")
    private String projectId;

    @Schema(description = "任务状态：0->未开始；1->进行中；2->已完成；3->已超时")
    @Range(min = 0, max = 3, message = "任务状态不正确")
    private Integer taskStatus;

    @Schema(description = "任务级别：0->一级；1->二级；2->三级")
    @Range(min = 0, max = 2, message = "任务级别不正确")
    private Integer taskGrade;

}