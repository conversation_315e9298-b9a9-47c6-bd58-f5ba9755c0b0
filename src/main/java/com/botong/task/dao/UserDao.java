package com.botong.task.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.botong.task.entity.UserEntity;
import org.apache.ibatis.annotations.Mapper;

/**
 * 研发人员
 *
 * <AUTHOR>
 */
@Mapper
public interface UserDao extends BaseDao<UserEntity> {

    default UserEntity getByUsername(String username) {
        return this.selectOne(new QueryWrapper<UserEntity>().eq("username", username));
    }
}