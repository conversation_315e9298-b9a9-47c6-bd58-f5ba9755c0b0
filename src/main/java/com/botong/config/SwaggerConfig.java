package com.botong.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Swagger配置
 */
@Configuration
public class SwaggerConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        Contact contact = new Contact();
        contact.setName("bto");
        OpenAPI openapi = new OpenAPI().info(new Info()
                .title("博通接口开发文档")
                .description("<div style='font-size:16px;color:red;'>开发者统一接口和协议</div>")
                .contact(contact)
                .version("1.0"));

        openapi.addSecurityItem(new SecurityRequirement().addList("api_key"))
                .components(new Components().addSecuritySchemes("api_key",
                        new SecurityScheme()
                                .name("Authorization")
                                .type(SecurityScheme.Type.APIKEY)
                                .in(SecurityScheme.In.HEADER)));

        return openapi;
    }

}