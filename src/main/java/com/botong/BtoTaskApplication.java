package com.botong;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * <AUTHOR>
 */
@SpringBootApplication
@MapperScan("com.botong.*.dao")
@EnableAspectJAutoProxy(exposeProxy = true)
public class BtoTaskApplication {

    public static void main(String[] args) {
        SpringApplication.run(BtoTaskApplication.class, args);
    }

}