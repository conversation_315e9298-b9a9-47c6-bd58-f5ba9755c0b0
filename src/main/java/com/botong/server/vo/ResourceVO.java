package com.botong.server.vo;

import com.botong.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 服务资源
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-21
 */
@Data
@Schema(description = "服务资源")
public class ResourceVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "服务ID")
    private String serverId;

    @Schema(description = "服务器IP地址")
    @NotNull(message = "服务器IP地址不能为空")
    private String serverIp;

    @Schema(description = "服务器到期时间")
    @NotNull(message = "服务器到期时间不能为空")
    @JsonFormat(pattern = DateUtils.DATE_PATTERN)
    private Date serverExpirationTime;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "平台合集", hidden = true)
    private List<PlatformVO> platformVos;

}