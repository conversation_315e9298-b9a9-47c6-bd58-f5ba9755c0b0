package com.botong.server.vo;

import com.botong.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 服务平台
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-21
 */
@Data
@Schema(description = "服务平台")
public class PlatformVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "平台ID")
    private String platformId;

    @Schema(description = "服务ID")
    @NotNull(message = "服务ID不能为空")
    private String serverId;

    @Schema(description = "平台名称")
    @NotNull(message = "平台名称不能为空")
    private String platformName;

    @Schema(description = "服务域名")
    @NotNull(message = "服务域名不能为空")
    private String serverDomainName;

    @Schema(description = "服务域名到期时间")
    @JsonFormat(pattern = DateUtils.DATE_PATTERN)
    private Date domainNameExpirationTime;

    @Schema(description = "SSL证书到期时间")
    @JsonFormat(pattern = DateUtils.DATE_PATTERN)
    private Date sslExpirationTime;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "资源路径合集", hidden = true)
    private List<ResourcePathVO> pathVoList;


}