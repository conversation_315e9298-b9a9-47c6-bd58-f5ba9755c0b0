package com.botong.server.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.botong.utils.DateUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> by 2022 on 2023/12/1.
 */
@Data
@EqualsAndHashCode
@ContentRowHeight(25)
@HeadRowHeight(40)
@ColumnWidth(25)
public class TaskExcelVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ExcelIgnore
    Map<String, String> transMap = new HashMap<>();

    @ExcelIgnore
    private String taskId;

    @ExcelProperty("事项名称")
    private String taskName;

    @ExcelProperty("开始时间")
    @DateTimeFormat(value = DateUtils.DATE_PATTERN)
    private Date startDate;

    @ExcelProperty("结束时间")
    @DateTimeFormat(value = DateUtils.DATE_PATTERN)
    private Date endDate;

    @ExcelProperty("任务级别")
    private String taskGradeLabel;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("研发人员")
    private String users;

    @ExcelIgnore
    private Integer taskGrade;

    @ExcelIgnore
    private List<String> userList;
}