package com.botong.server.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 服务资源路径
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-21
 */
@Data
@Schema(description = "服务资源路径")
public class ResourcePathVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "路径ID")
    private String pathId;

    @Schema(description = "平台ID")
    @NotNull(message = "平台ID不能为空")
    private String platformId;

    @Schema(description = "资源文件路径")
    @NotNull(message = "资源文件路径不能为空")
    private String filePath;

    @Schema(description = "说明")
    private String description;


}