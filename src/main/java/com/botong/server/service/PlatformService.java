package com.botong.server.service;

import com.botong.common.PageResult;
import com.botong.server.entity.PlatformEntity;
import com.botong.server.query.PlatformQuery;
import com.botong.server.vo.PlatformVO;
import com.botong.task.service.BaseService;

import java.util.List;

/**
 * 服务平台
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-21
 */
public interface PlatformService extends BaseService<PlatformEntity> {

    PageResult<PlatformVO> page(PlatformQuery query);

    void save(PlatformVO vo);

    void update(PlatformVO vo);

    void delete(List<String> idList);

    PlatformVO getInfoById(String id);

    List<PlatformVO> getPlatformListByServerId(String serverId);
}