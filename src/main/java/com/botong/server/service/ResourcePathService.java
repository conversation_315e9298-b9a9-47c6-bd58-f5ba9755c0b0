package com.botong.server.service;

import com.botong.server.entity.ResourcePathEntity;
import com.botong.server.vo.ResourcePathVO;
import com.botong.task.service.BaseService;

import java.util.List;

/**
 * 服务资源路径
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-21
 */
public interface ResourcePathService extends BaseService<ResourcePathEntity> {

    void save(ResourcePathVO vo);

    void update(ResourcePathVO vo);

    void delete(List<String> idList);

    List<ResourcePathVO> getByPlatformId(String platformId);
}