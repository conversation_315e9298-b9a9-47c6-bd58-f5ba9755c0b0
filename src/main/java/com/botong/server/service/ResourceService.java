package com.botong.server.service;

import com.botong.common.PageResult;
import com.botong.server.entity.ResourceEntity;
import com.botong.server.query.ResourceQuery;
import com.botong.server.vo.ResourceVO;
import com.botong.task.service.BaseService;

import java.util.List;

/**
 * 服务资源
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-21
 */
public interface ResourceService extends BaseService<ResourceEntity> {

    PageResult<ResourceVO> page(ResourceQuery query);

    void save(ResourceVO vo);

    void update(ResourceVO vo);

    void delete(List<String> idList);

    ResourceVO getInfoById(String id);
}