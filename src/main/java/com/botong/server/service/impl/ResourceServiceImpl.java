package com.botong.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.common.PageResult;
import com.botong.server.convert.ResourceConvert;
import com.botong.server.dao.ResourceDao;
import com.botong.server.entity.ResourceEntity;
import com.botong.server.query.ResourceQuery;
import com.botong.server.service.PlatformService;
import com.botong.server.service.ResourceService;
import com.botong.server.vo.PlatformVO;
import com.botong.server.vo.ResourceVO;
import com.botong.task.service.impl.BaseServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 服务资源
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-21
 */
@Service
@AllArgsConstructor
public class ResourceServiceImpl extends BaseServiceImpl<ResourceDao, ResourceEntity> implements ResourceService {

    private final PlatformService platformService;

    @Override
    public PageResult<ResourceVO> page(ResourceQuery query) {
        IPage<ResourceEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));
        List<ResourceVO> resourceVos = ResourceConvert.INSTANCE.convertList(page.getRecords());
        resourceVos.forEach(resourceVO -> {
            resourceVO.setPlatformVos(getPlatformListByServerId(resourceVO.getServerId()));
        });
        return new PageResult<>(resourceVos, page.getTotal());
    }

    private LambdaQueryWrapper<ResourceEntity> getWrapper(ResourceQuery query) {
        LambdaQueryWrapper<ResourceEntity> wrapper = Wrappers.lambdaQuery();

        return wrapper;
    }

    @Override
    public void save(ResourceVO vo) {
        ResourceEntity entity = ResourceConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    @Override
    public void update(ResourceVO vo) {
        ResourceEntity entity = ResourceConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<String> idList) {
        removeByIds(idList);
    }

    @Override
    public ResourceVO getInfoById(String id) {
        ResourceVO resourceVo = ResourceConvert.INSTANCE.convert(getById(id));
        resourceVo.setPlatformVos(getPlatformListByServerId(resourceVo.getServerId()));
        return resourceVo;
    }

    public List<PlatformVO> getPlatformListByServerId(String serverId) {
        return platformService.getPlatformListByServerId(serverId);
    }


}