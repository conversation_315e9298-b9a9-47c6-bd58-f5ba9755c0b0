package com.botong.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.botong.server.convert.ResourcePathConvert;
import com.botong.server.dao.ResourcePathDao;
import com.botong.server.entity.ResourcePathEntity;
import com.botong.server.service.ResourcePathService;
import com.botong.server.vo.ResourcePathVO;
import com.botong.task.service.impl.BaseServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 服务资源路径
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-21
 */
@Service
@AllArgsConstructor
public class ResourcePathServiceImpl extends BaseServiceImpl<ResourcePathDao, ResourcePathEntity> implements ResourcePathService {

    @Override
    public void save(ResourcePathVO vo) {
        ResourcePathEntity entity = ResourcePathConvert.INSTANCE.convert(vo);
        baseMapper.insert(entity);
    }

    @Override
    public void update(ResourcePathVO vo) {
        ResourcePathEntity entity = ResourcePathConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<String> idList) {
        removeByIds(idList);
    }

    @Override
    public List<ResourcePathVO> getByPlatformId(String platformId) {
        LambdaQueryWrapper<ResourcePathEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ResourcePathEntity::getPlatformId, platformId);
        return ResourcePathConvert.INSTANCE.convertList(baseMapper.selectList(wrapper));
    }

}