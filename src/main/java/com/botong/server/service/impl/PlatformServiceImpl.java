package com.botong.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.common.PageResult;
import com.botong.server.convert.PlatformConvert;
import com.botong.server.dao.PlatformDao;
import com.botong.server.entity.PlatformEntity;
import com.botong.server.query.PlatformQuery;
import com.botong.server.service.PlatformService;
import com.botong.server.service.ResourcePathService;
import com.botong.server.vo.PlatformVO;
import com.botong.server.vo.ResourcePathVO;
import com.botong.task.service.impl.BaseServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 服务平台
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-21
 */
@Service
@AllArgsConstructor
public class PlatformServiceImpl extends BaseServiceImpl<PlatformDao, PlatformEntity> implements PlatformService {

    private final ResourcePathService resourcePathService;

    @Override
    public PageResult<PlatformVO> page(PlatformQuery query) {
        IPage<PlatformEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));
        List<PlatformVO> platformVos = PlatformConvert.INSTANCE.convertList(page.getRecords());
        return new PageResult<>(encapsulation(platformVos), page.getTotal());
    }

    public List<PlatformVO> encapsulation(List<PlatformVO> platformVos) {
        platformVos.forEach(platformVO -> {
            platformVO.setPathVoList(getResourcePathListByPlatformId(platformVO.getPlatformId()));
        });
        return platformVos;
    }

    private LambdaQueryWrapper<PlatformEntity> getWrapper(PlatformQuery query) {
        LambdaQueryWrapper<PlatformEntity> wrapper = Wrappers.lambdaQuery();

        return wrapper;
    }

    @Override
    public void save(PlatformVO vo) {
        PlatformEntity entity = PlatformConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    @Override
    public void update(PlatformVO vo) {
        PlatformEntity entity = PlatformConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<String> idList) {
        removeByIds(idList);
    }

    @Override
    public PlatformVO getInfoById(String id) {
        PlatformEntity entity = getById(id);
        List<ResourcePathVO> resourcePathVo = getResourcePathListByPlatformId(entity.getPlatformId());
        PlatformVO platformVo = PlatformConvert.INSTANCE.convert(entity);
        platformVo.setPathVoList(resourcePathVo);
        return platformVo;
    }

    @Override
    public List<PlatformVO> getPlatformListByServerId(String serverId) {
        LambdaQueryWrapper<PlatformEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PlatformEntity::getServerId, serverId);
        return encapsulation(PlatformConvert.INSTANCE.convertList(baseMapper.selectList(wrapper)));
    }

    public List<ResourcePathVO> getResourcePathListByPlatformId(String platformId) {
        return resourcePathService.getByPlatformId(platformId);
    }

}