package com.botong.server.controller;

import com.botong.common.PageResult;
import com.botong.common.Result;
import com.botong.server.query.PlatformQuery;
import com.botong.server.service.PlatformService;
import com.botong.server.service.ResourcePathService;
import com.botong.server.vo.PlatformVO;
import com.botong.server.vo.ResourcePathVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 服务平台
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-21
 */
@RestController
@RequestMapping("platform")
@Tag(name = "服务平台")
@AllArgsConstructor
@Validated
public class PlatformController {
    private final PlatformService platformService;
    private final ResourcePathService resourcePathService;

    @PostMapping("page")
    @Operation(summary = "分页")
    public Result<PageResult<PlatformVO>> page(@Valid @RequestBody PlatformQuery query) {
        PageResult<PlatformVO> page = platformService.page(query);

        return Result.ok(page);
    }

    @GetMapping("{id}")
    @Operation(summary = "信息")
    public Result<PlatformVO> get(@PathVariable("id") String id) {
        PlatformVO platformVo = platformService.getInfoById(id);
        return Result.ok(platformVo);
    }

    @PostMapping
    @Operation(summary = "保存")
    public Result<String> save(@RequestBody @Valid PlatformVO vo) {
        platformService.save(vo);

        return Result.ok();
    }

    @PutMapping
    @Operation(summary = "修改")
    public Result<String> update(@RequestBody @Valid PlatformVO vo) {
        platformService.update(vo);

        return Result.ok();
    }

    @DeleteMapping
    @Operation(summary = "删除")
    public Result<String> delete(@RequestBody List<String> idList) {
        platformService.delete(idList);

        return Result.ok();
    }

    @PostMapping("saveResourcePath")
    @Operation(summary = "保存资源路径")
    public Result<String> saveResourcePath(@RequestBody @Valid ResourcePathVO vo) {
        resourcePathService.save(vo);

        return Result.ok();
    }

    @PutMapping("updateResourcePath")
    @Operation(summary = "修改资源路径")
    public Result<String> updateResourcePath(@RequestBody @Valid ResourcePathVO vo) {
        resourcePathService.update(vo);

        return Result.ok();
    }

    @DeleteMapping("deleteResourcePath")
    @Operation(summary = "删除资源路径")
    public Result<String> deleteResourcePath(@RequestBody List<String> idList) {
        resourcePathService.delete(idList);

        return Result.ok();
    }
}