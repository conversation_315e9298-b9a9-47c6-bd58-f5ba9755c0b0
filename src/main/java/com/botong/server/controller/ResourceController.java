package com.botong.server.controller;

import com.botong.common.PageResult;
import com.botong.common.Result;
import com.botong.server.query.ResourceQuery;
import com.botong.server.service.ResourceService;
import com.botong.server.vo.ResourceVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 服务资源
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-21
 */
@RestController
@RequestMapping("resource")
@Tag(name = "服务资源")
@AllArgsConstructor
@Validated
public class ResourceController {
    private final ResourceService resourceService;

    @PostMapping("page")
    @Operation(summary = "分页")
    public Result<PageResult<ResourceVO>> page(@Valid @RequestBody ResourceQuery query) {
        PageResult<ResourceVO> page = resourceService.page(query);

        return Result.ok(page);
    }

    @GetMapping("{id}")
    @Operation(summary = "信息")
    public Result<ResourceVO> get(@PathVariable("id") String id) {
        ResourceVO resourceVO = resourceService.getInfoById(id);

        return Result.ok(resourceVO);
    }

    @PostMapping
    @Operation(summary = "保存")
    public Result<String> save(@RequestBody @Valid ResourceVO vo) {
        resourceService.save(vo);

        return Result.ok();
    }

    @PutMapping
    @Operation(summary = "修改")
    public Result<String> update(@RequestBody @Valid ResourceVO vo) {
        resourceService.update(vo);

        return Result.ok();
    }

    @DeleteMapping
    @Operation(summary = "删除")
    public Result<String> delete(@RequestBody List<String> idList) {
        resourceService.delete(idList);

        return Result.ok();
    }
}