package com.botong.server.convert;

import com.botong.server.entity.ResourcePathEntity;
import com.botong.server.vo.ResourcePathVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 服务资源路径
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-21
 */
@Mapper
public interface ResourcePathConvert {
    ResourcePathConvert INSTANCE = Mappers.getMapper(ResourcePathConvert.class);

    ResourcePathEntity convert(ResourcePathVO vo);

    ResourcePathVO convert(ResourcePathEntity entity);

    List<ResourcePathVO> convertList(List<ResourcePathEntity> list);

}