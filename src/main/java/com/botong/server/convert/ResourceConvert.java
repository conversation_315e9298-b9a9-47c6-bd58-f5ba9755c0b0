package com.botong.server.convert;

import com.botong.server.entity.ResourceEntity;
import com.botong.server.vo.ResourceVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 服务资源
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-21
 */
@Mapper
public interface ResourceConvert {
    ResourceConvert INSTANCE = Mappers.getMapper(ResourceConvert.class);

    ResourceEntity convert(ResourceVO vo);

    ResourceVO convert(ResourceEntity entity);

    List<ResourceVO> convertList(List<ResourceEntity> list);

}