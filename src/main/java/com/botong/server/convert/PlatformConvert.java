package com.botong.server.convert;

import com.botong.server.entity.PlatformEntity;
import com.botong.server.vo.PlatformVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 服务平台
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-21
 */
@Mapper
public interface PlatformConvert {
    PlatformConvert INSTANCE = Mappers.getMapper(PlatformConvert.class);

    PlatformEntity convert(PlatformVO vo);

    PlatformVO convert(PlatformEntity entity);

    List<PlatformVO> convertList(List<PlatformEntity> list);

}