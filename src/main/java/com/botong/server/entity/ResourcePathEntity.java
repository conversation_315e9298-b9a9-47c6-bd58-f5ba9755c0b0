package com.botong.server.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 服务资源路径
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-21
 */
@Data
@TableName("server_resource_path")
public class ResourcePathEntity {
    /**
     * 路径ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String pathId;

    /**
     * 平台ID
     */
    private String platformId;

    /**
     * 资源文件路径
     */
    private String filePath;

    /**
     * 说明
     */
    private String description;

}