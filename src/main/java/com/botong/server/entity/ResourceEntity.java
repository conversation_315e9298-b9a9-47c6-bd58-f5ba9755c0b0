package com.botong.server.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 服务资源
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-21
 */
@Data
@TableName("server_resource")
public class ResourceEntity {
    /**
     * 服务ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String serverId;

    /**
     * 服务器IP地址
     */
    private String serverIp;

    /**
     * 服务器到期时间
     */
    private Date serverExpirationTime;

    /**
     * 备注
     */
    private String remark;

}