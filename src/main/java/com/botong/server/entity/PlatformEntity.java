package com.botong.server.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 服务平台
 *
 * <AUTHOR>
 * @since 1.0.0 2023-11-21
 */

@Data
@TableName("server_platform")
public class PlatformEntity {
    /**
     * 平台ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String platformId;

    /**
     * 服务ID
     */
    private String serverId;

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 服务域名
     */
    private String serverDomainName;

    /**
     * 服务域名到期时间
     */
    private Date domainNameExpirationTime;

    /**
     * SSL证书到期时间
     */
    private Date sslExpirationTime;

    /**
     * 备注
     */
    private String remark;

}