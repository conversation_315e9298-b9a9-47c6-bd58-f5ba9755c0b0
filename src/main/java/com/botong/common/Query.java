package com.botong.common;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 查询公共参数
 * <AUTHOR>
 */
@Data
public class Query {
    @NotNull(message = "当前页码不能为空")
    @Min(value = 1, message = "当前页码最小值为1")
    @Schema(description = "当前页码", required = true)
    Integer page;

    @NotNull(message = "页码大小不能为空")
    @Range(min = 1, max = 200, message = "页码大小，取值范围1-200")
    @Schema(description = "页码大小", required = true)
    Integer limit;

    @Schema(description = "排序字段")
    String order;

    @Schema(description = "是否升序 true->是 false->否")
    boolean asc;

}