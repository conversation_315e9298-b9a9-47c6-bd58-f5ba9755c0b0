package com.botong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR> by 2022 on 2023/11/18.
 */

@Getter
@AllArgsConstructor
public enum TaskStatusEnum {

    NOT_STARTED(0, "未开始"),
    UNDERWAY(1, "进行中"),
    COMPLETED(2, "已完成"),
    TIMEOUT(3, "已超时");

    private final Integer value;
    private final String name;

    public static String getNameByValue(Integer value) {
        for (TaskStatusEnum s : TaskStatusEnum.values()) {
            if (s.getValue().equals(value)) {
                return s.getName();
            }
        }
        return null;
    }

    public static Integer getValueByName(String name) {
        for (TaskStatusEnum s : TaskStatusEnum.values()) {
            if (Objects.equals(s.getName(), name)) {
                return s.getValue();
            }
        }
        return null;
    }

}