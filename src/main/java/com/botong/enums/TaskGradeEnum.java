package com.botong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR> by 2022 on 2023/11/18.
 */
@Getter
@AllArgsConstructor
public enum TaskGradeEnum {

    FIRST_ORDER(0, "一级"),
    SECOND_ORDER(1, "二级"),
    THREE_ORDER(2, "三级");

    private final Integer value;
    private final String name;

    public static String getNameByValue(Integer value) {
        for (TaskGradeEnum s : TaskGradeEnum.values()) {
            if (s.getValue().equals(value)) {
                return s.getName();
            }
        }
        return null;
    }

    public static Integer getValueByName(String name) {
        for (TaskGradeEnum s : TaskGradeEnum.values()) {
            if (Objects.equals(s.getName(), name)) {
                return s.getValue();
            }
        }
        return null;
    }

}