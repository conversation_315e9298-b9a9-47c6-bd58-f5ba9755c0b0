package com.botong.security.user;

import org.springframework.security.core.context.SecurityContextHolder;

/**
 * 用户
 *
 * <AUTHOR>
 */
public class SecurityUser {

    /**
     * 获取用户信息
     */
    public static UserDetail getUser() {
        UserDetail user;
        try {
            user = (UserDetail) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        } catch (Exception e) {
            return new UserDetail();
        }
        return user;
    }

    /**
     * 获取用户ID
     */
    public static String getUserId() {
        return getUser().getUserId();
    }

}